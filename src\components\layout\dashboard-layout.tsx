
import React, { useState } from 'react';
import { NavLink, useNavigate, Outlet, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import {
  LayoutGrid,
  ClipboardCheck,
  AlertTriangle,
  FileText,
  Settings,
  LogOut,
  Menu,
  X,
  ChevronRight,
  ClipboardList,
  Users,
  ShieldAlert,
  FileWarning,
  FolderOpen,
  ShieldCheck,
  User,
  ChevronDown,
  Bell,
  HelpCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Dialog,
  DialogContent,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';


// Mobile Navigation Component
const MobileNav: React.FC<{
  navItems: Array<{
    to: string;
    icon: React.ReactNode;
    label: string;
    requiredRole: string;
    end?: boolean;
  }>;
  hasPermission: (role: string) => boolean;
  onLogout: () => void;
  currentUser: any;
}> = ({ navItems, hasPermission, onLogout, currentUser }) => {
  const [open, setOpen] = useState(false);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden hover:bg-primary/10">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <div className="grid gap-6 py-4">
          {/* User Info Section */}
          {currentUser && (
            <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
              <Avatar className="h-10 w-10">
                <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                  {currentUser.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <p className="font-medium text-sm truncate">{currentUser.name}</p>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="text-xs capitalize">
                    {currentUser.role}
                  </Badge>
                </div>
              </div>
            </div>
          )}

          {/* Navigation Items */}
          <nav className="space-y-1">
            {navItems.map((item) => (
              hasPermission(item.requiredRole) && (
                <NavLink
                  key={item.to}
                  to={item.to}
                  end={item.end}
                  onClick={() => setOpen(false)}
                  className={({ isActive }) => cn(
                    "flex items-center px-3 py-3 rounded-lg hover:bg-primary/10 transition-colors duration-150",
                    isActive ? "bg-primary/10 text-primary font-medium" : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  <span className="flex-shrink-0">{item.icon}</span>
                  <span className="ml-3 font-medium">{item.label}</span>
                </NavLink>
              )
            ))}

            {/* Logout Button */}
            <div className="pt-2 border-t">
              <Button
                variant="ghost"
                onClick={() => {
                  onLogout();
                  setOpen(false);
                }}
                className="w-full justify-start text-muted-foreground hover:text-foreground hover:bg-destructive/10"
              >
                <LogOut className="h-5 w-5 mr-3" />
                <span className="font-medium">Logout</span>
              </Button>
            </div>
          </nav>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export function DashboardLayout() {
  const { currentUser, logout, hasPermission } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out"
      });
      navigate('/login');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to logout",
        variant: "destructive"
      });
    }
  };

  const navItems = [
    {
      to: '/dashboard',
      icon: <LayoutGrid className="h-5 w-5" />,
      label: 'Dashboard',
      requiredRole: 'viewer',
      end: true
    },
    {
      to: '/prerequisite-programs',
      icon: <ClipboardList className="h-5 w-5" />,
      label: 'Prerequisite Programs',
      requiredRole: 'viewer'
    },
    {
      to: '/food-safety-culture',
      icon: <Users className="h-5 w-5" />,
      label: 'Food Safety Culture',
      requiredRole: 'viewer'
    },
    {
      to: '/allergen-management',
      icon: <ShieldAlert className="h-5 w-5" />,
      label: 'Allergen Management',
      requiredRole: 'viewer'
    },
    {
      to: '/capa',
      icon: <FileWarning className="h-5 w-5" />,
      label: 'CAPA Management',
      requiredRole: 'viewer'
    },
    {
      to: '/document-management',
      icon: <FolderOpen className="h-5 w-5" />,
      label: 'Document Management',
      requiredRole: 'viewer'
    },
    {
      to: '/hazard-analysis',
      icon: <AlertTriangle className="h-5 w-5" />,
      label: 'Hazard Analysis',
      requiredRole: 'viewer'
    },
    {
      to: '/ccp-management',
      icon: <ClipboardCheck className="h-5 w-5" />,
      label: 'CCP Management',
      requiredRole: 'viewer'
    },
    {
      to: '/plan-generator',
      icon: <FileText className="h-5 w-5" />,
      label: 'Plan Generator',
      requiredRole: 'viewer'
    },
    {
      to: '/settings',
      icon: <Settings className="h-5 w-5" />,
      label: 'Settings',
      requiredRole: 'qa'
    }
  ];

  // Get current page info
  const getCurrentPageInfo = () => {
    const currentPath = location.pathname;
    const currentItem = navItems.find(item =>
      item.end ? currentPath === item.to : currentPath.startsWith(item.to)
    );
    return currentItem || { label: 'Dashboard', icon: <LayoutGrid className="h-5 w-5" /> };
  };

  const currentPageInfo = getCurrentPageInfo();

  return (
    <div className="min-h-screen flex flex-col">
        {/* Header */}
        <header className="bg-gradient-to-r from-background via-background to-primary/5 border-b shadow-sm z-10 backdrop-blur-sm">
          <div className="mx-auto px-4 lg:px-6 flex h-16 items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
                className="hidden md:flex hover:bg-primary/10 transition-colors"
              >
                {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
              <MobileNav
                navItems={navItems}
                hasPermission={hasPermission}
                onLogout={handleLogout}
                currentUser={currentUser}
              />
              <div className="flex items-center gap-3">
                <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-lg">
                  <ShieldCheck className="h-5 w-5 text-primary" />
                </div>
                <div className="flex flex-col">
                  <h1 className="text-xl font-bold text-foreground leading-none">
                    <span className="hidden sm:inline">HACCP Plan Pilot</span>
                    <span className="sm:hidden">HACCP</span>
                  </h1>
                  <p className="text-xs text-muted-foreground font-medium hidden sm:block">
                    Food Safety Management System
                  </p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {currentUser && (
                <>
                  {/* Notifications Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="hidden lg:flex hover:bg-primary/10 relative"
                    aria-label="Notifications"
                  >
                    <Bell className="h-5 w-5" />
                    {/* Notification badge */}
                    <span className="absolute -top-1 -right-1 h-3 w-3 bg-destructive rounded-full text-[10px] font-bold text-destructive-foreground flex items-center justify-center">
                      3
                    </span>
                  </Button>

                  {/* Help Button */}
                  <Button
                    variant="ghost"
                    size="icon"
                    className="hidden lg:flex hover:bg-primary/10"
                    aria-label="Help"
                  >
                    <HelpCircle className="h-5 w-5" />
                  </Button>

                  {/* User Profile Dropdown */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        className="hidden md:flex items-center gap-3 px-3 py-2 h-auto hover:bg-primary/10 transition-colors"
                      >
                        <Avatar className="h-8 w-8">
                          <AvatarFallback className="bg-primary/10 text-primary font-semibold text-sm">
                            {currentUser.name.split(' ').map((n: string) => n[0]).join('').toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div className="text-left hidden lg:block">
                          <p className="font-medium text-sm leading-none">{currentUser.name}</p>
                          <p className="text-xs text-muted-foreground mt-1 capitalize">{currentUser.role}</p>
                        </div>
                        <ChevronDown className="h-4 w-4 text-muted-foreground hidden lg:block" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-56">
                      <DropdownMenuLabel className="font-normal">
                        <div className="flex flex-col space-y-1">
                          <p className="text-sm font-medium leading-none">{currentUser.name}</p>
                          <p className="text-xs leading-none text-muted-foreground">
                            {currentUser.email}
                          </p>
                        </div>
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="cursor-pointer">
                        <User className="mr-2 h-4 w-4" />
                        <span>Profile</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="cursor-pointer">
                        <Settings className="mr-2 h-4 w-4" />
                        <span>Settings</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem className="cursor-pointer">
                        <HelpCircle className="mr-2 h-4 w-4" />
                        <span>Help & Support</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="cursor-pointer text-destructive focus:text-destructive"
                        onClick={handleLogout}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        <span>Logout</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </>
              )}
            </div>
          </div>
        </header>

        {/* Breadcrumb Section */}
        <div className="bg-muted/30 border-b px-4 lg:px-6 py-3">
          <div className="flex items-center gap-2 text-sm">
            <div className="flex items-center gap-2 text-muted-foreground">
              <span>HACCP Plan Pilot</span>
              <ChevronRight className="h-4 w-4" />
            </div>
            <div className="flex items-center gap-2 font-medium">
              {currentPageInfo.icon}
              <span>{currentPageInfo.label}</span>
            </div>
          </div>
        </div>

      <div className="flex flex-1">
        {/* Sidebar */}
        <aside
          className={cn(
            "bg-muted border-r transition-all duration-200 ease-in-out z-10",
            sidebarOpen ? "w-64" : "w-16"
          )}
        >
          <nav className="p-2 space-y-1">
            {navItems.map((item) => (
              hasPermission(item.requiredRole as any) && (
                <NavLink
                  key={item.to}
                  to={item.to}
                  end={item.end}
                  className={({ isActive }) => cn(
                    "flex items-center px-3 py-2 rounded-md hover:bg-muted-foreground/10 transition-colors duration-150",
                    isActive ? "bg-muted-foreground/10 text-primary font-medium" : "text-muted-foreground",
                    sidebarOpen ? "justify-start" : "justify-center"
                  )}
                >
                  <span className="flex-shrink-0">{item.icon}</span>
                  {sidebarOpen && <span className="ml-3">{item.label}</span>}
                </NavLink>
              )
            ))}
          </nav>
        </aside>

        {/* Main content */}
        <main className="flex-1 overflow-auto p-6">
          <Outlet />
        </main>
      </div>
    </div>
  );
}

export default DashboardLayout;
